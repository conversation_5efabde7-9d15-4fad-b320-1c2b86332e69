package com.ruoyi.common.aspect;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.annotation.BusinessLog;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.domain.BusinessLogContext;
import com.ruoyi.common.domain.FieldChange;
import com.ruoyi.common.enums.BusinessClass;
import com.ruoyi.common.enums.OperationType;
import com.ruoyi.common.service.BusinessLogService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ip.IpUtils;

/**
 * 业务操作日志切面
 * 
 * <AUTHOR>
 */
@Aspect
@Component
public class BusinessLogAspect {
    
    private static final Logger logger = LoggerFactory.getLogger(BusinessLogAspect.class);
    private static final Logger operationLogger = LoggerFactory.getLogger("operationLog");
    
    @Autowired
    private BusinessLogService businessLogService;

    @Autowired
    private Environment environment;

    private static final ThreadLocal<Boolean> isLogging = new ThreadLocal<>();
    
    /**
     * 环绕通知处理业务日志
     */
    @Around("@annotation(businessLog)")
    public Object handleBusinessLog(ProceedingJoinPoint joinPoint, BusinessLog businessLog) throws Throwable {
        operationLogger.warn("========== BusinessLogAspect被触发 ==========");
        operationLogger.warn("目标方法: {}", joinPoint.getSignature().getName());
        operationLogger.warn("业务类型: {}", businessLog.businessClass());
        operationLogger.warn("操作类型: {}", businessLog.operationType());
        
        // 如果当前线程已在记录日志，则直接执行，避免嵌套日志
        if (Boolean.TRUE.equals(isLogging.get())) {
            operationLogger.warn("当前线程已在记录日志，跳过");
            return joinPoint.proceed();
        }

        // 如果禁用了日志记录，直接执行方法
        if (!businessLog.enabled()) {
            operationLogger.warn("日志记录已禁用，跳过");
            return joinPoint.proceed();
        }

        isLogging.set(true);
        long startTime = System.currentTimeMillis();
        BusinessLogContext context = new BusinessLogContext();
        context.setOperationTime(new java.util.Date(startTime)); // 设置操作时间
        Object result = null;
        Exception exception = null;

        try {
            // 1. 初始化上下文（不包括业务ID和结果）
            initializeContext(joinPoint, businessLog, context);
            
            // 2. 记录执行前数据
            recordBeforeData(joinPoint, context);
            
            // 3. 执行目标方法
            result = joinPoint.proceed();
            context.setSuccess(true);

            return result;
            
        } catch (Exception e) {
            exception = e;
            context.setSuccess(false);
            context.setErrorMessage(e.getMessage());
            throw e;
        } finally {
            try {
                long executionTime = System.currentTimeMillis() - startTime;
                context.setExecutionTime(executionTime);

                // 检查是否为批量删除操作
                if (context.getOperationType() == OperationType.DELETE && isBatchDelete(joinPoint)) {
                    handleBatchDelete(joinPoint, context, result);
                } else {
                    // 记录执行后数据（包括从结果中提取业务ID）
                    recordAfterData(joinPoint, context, result);
                    operationLogger.warn("准备记录日志，上下文: businessId={}, businessType={}, operationType={}", 
                        context.getBusinessId(), context.getBusinessClass(), context.getOperationType());
                    businessLogService.recordLog(context);
                    operationLogger.warn("日志记录完成");
                    // // 在测试环境中同步记录日志，以保证测试的确定性
                    // if (isTestProfileActive()) {
                    //     businessLogService.recordLog(context);
                    // } else {
                    //     // 异步记录单条日志
                    //     businessLogService.recordLogAsync(context);
                    // }
                }
            } catch (Exception logEx) {
                operationLogger.error("记录业务操作日志时发生严重错误", logEx);
            } finally {
                isLogging.remove(); // 确保在处理结束时移除ThreadLocal变量
            }
        }
    }

    private boolean isBatchDelete(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Parameter[] parameters = signature.getMethod().getParameters();
        for (int i = 0; i < parameters.length; i++) {
            if (parameters[i].isAnnotationPresent(PathVariable.class)) {
                return joinPoint.getArgs()[i] instanceof Long[];
            }
        }
        return false;
    }

    private void handleBatchDelete(ProceedingJoinPoint joinPoint, BusinessLogContext baseContext, Object result) {
        Object[] args = joinPoint.getArgs();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Parameter[] parameters = signature.getMethod().getParameters();

        for (int i = 0; i < parameters.length; i++) {
            if (parameters[i].isAnnotationPresent(PathVariable.class) && args[i] instanceof Long[]) {
                Long[] ids = (Long[]) args[i];
                for (Long id : ids) {
                    BusinessLogContext individualContext = new BusinessLogContext(baseContext);
                    individualContext.setBusinessId(id);
                    if (result != null) {
                        individualContext.setResponseResult(JSON.toJSONString(result));
                    }
                    businessLogService.recordLogAsync(individualContext);
                }
                return; // 假设只有一个ID数组参数
            }
        }
    }
    
    /**
     * 初始化日志上下文
     */
    private void initializeContext(ProceedingJoinPoint joinPoint, BusinessLog businessLog, BusinessLogContext context) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        context.setMethodName(method.getName());
        
        BusinessClass businessClass = determineBusinessClass(joinPoint, businessLog);
        context.setBusinessClass(businessClass);

        OperationType operationType = determineOperationType(joinPoint, businessLog);
        context.setOperationType(operationType);
        
        String description = determineDescription(businessLog, businessClass, operationType);
        context.setDescription(description);
        
        setOperatorInfo(context);
        setRequestInfo(context, joinPoint);
        
        logger.debug("初始化业务日志上下文完成: {}", context);
    }
    
    /**
     * 确定业务类型
     */
    private BusinessClass determineBusinessClass(ProceedingJoinPoint joinPoint, BusinessLog businessLog) {
        if (businessLog.businessClass() != BusinessClass.AUTO) {
            return businessLog.businessClass();
        }
        String className = joinPoint.getTarget().getClass().getSimpleName();
        if (className.contains("Lead")) return BusinessClass.LEAD;
        if (className.contains("Contact")) return BusinessClass.CONTACT;
        if (className.contains("Customer")) return BusinessClass.CUSTOMER;
        if (className.contains("Opportunity")) return BusinessClass.OPPORTUNITY;
        if (className.contains("Contract")) return BusinessClass.CONTRACT;
        if (className.contains("VisitPlan")) return BusinessClass.VISIT_PLAN;
        if (className.contains("Team")) return BusinessClass.TEAM;
        if (className.contains("Payment")) return BusinessClass.PAYMENT;
        if (className.contains("Reconciliation")) return BusinessClass.RECONCILIATION;
        return BusinessClass.AUTO;
    }
    
    /**
     * 确定操作类型
     */
    private OperationType determineOperationType(ProceedingJoinPoint joinPoint, BusinessLog businessLog) {
        if (businessLog.operationType() != OperationType.AUTO) {
            return businessLog.operationType();
        }
        if (StringUtils.hasText(businessLog.value())) {
            return OperationType.getByCode(businessLog.value());
        }
        String methodName = joinPoint.getSignature().getName();
        return OperationType.getByMethodName(methodName);
    }
    
    /**
     * 确定描述信息
     */
    private String determineDescription(BusinessLog businessLog, BusinessClass businessClass, OperationType operationType) {
        if (StringUtils.hasText(businessLog.description())) {
            return businessLog.description();
        }
        if (StringUtils.hasText(businessLog.template())) {
            return businessLog.template();
        }
        return operationType.getName() + businessClass.getName();
    }
    
    /**
     * 设置操作人信息
     */
    private void setOperatorInfo(BusinessLogContext context) {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null) {
                context.setOperatorId(loginUser.getUserId());
                context.setOperatorName(loginUser.getUsername());
            }
        } catch (Exception e) {
            logger.warn("获取操作人信息失败", e);
        }
    }
    
    /**
     * 设置请求信息
     */
    private void setRequestInfo(BusinessLogContext context, ProceedingJoinPoint joinPoint) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                context.setIpAddress(IpUtils.getIpAddr(request));
                context.setUserAgent(request.getHeader("User-Agent"));
                
                Object[] args = joinPoint.getArgs();
                if (args != null && args.length > 0) {
                    context.setRequestParams(JSON.toJSONString(args));
                }
            }
        } catch (Exception e) {
            logger.warn("获取请求信息失败", e);
        }
    }
    
    /**
     * 智能提取业务ID
     */
    private void extractBusinessId(ProceedingJoinPoint joinPoint, BusinessLogContext context, Object result) {
        operationLogger.warn("开始提取业务ID，返回结果类型: {}", result != null ? result.getClass().getSimpleName() : "null");
        
        // 1. 优先从返回结果中提取ID (适用于创建操作)
        if (result instanceof AjaxResult) {
            AjaxResult ajaxResult = (AjaxResult) result;
            operationLogger.warn("AjaxResult - 成功: {}, 数据: {}", ajaxResult.isSuccess(), ajaxResult.get("data"));
            if (ajaxResult.isSuccess() && ajaxResult.get("data") != null) {
                Long id = getIdFromObject(ajaxResult.get("data"));
                operationLogger.warn("从AjaxResult中提取到ID: {}", id);
                if (id != null) {
                    context.setBusinessId(id);
                    operationLogger.warn("设置业务ID为: {}", id);
                    return;
                }
            }
        }

        // 2. 从请求参数中提取ID
        Object[] args = joinPoint.getArgs();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Parameter[] parameters = signature.getMethod().getParameters();

        // 2.1 从 @RequestBody 中提取
        for (int i = 0; i < parameters.length; i++) {
            if (parameters[i].isAnnotationPresent(RequestBody.class)) {
                if (args[i] != null) {
                    Long id = getIdFromObject(args[i]);
                    if (id != null) {
                        context.setBusinessId(id);
                        return;
                    }
                }
            }
        }

        // 2.2 从 @PathVariable 中提取
        for (int i = 0; i < parameters.length; i++) {
            if (parameters[i].isAnnotationPresent(PathVariable.class)) {
                PathVariable pathVariable = parameters[i].getAnnotation(PathVariable.class);
                String paramName = StringUtils.hasText(pathVariable.value()) ? pathVariable.value() : pathVariable.name();
                if (paramName.toLowerCase().contains("id")) {
                    if (args[i] instanceof Long) {
                        context.setBusinessId((Long) args[i]);
                        return;
                    }
                    // 批量ID的处理已移至 handleBatchDelete
                }
            }
        }
    }
    
    /**
     * 从对象中灵活获取ID (id, leadId, customerId等)
     */
    private Long getIdFromObject(Object obj) {
        if (obj == null) return null;
        // 优先尝试 getLeadId, getCustomerId 等特定方法
        String[] idGetters = {"getId", "getLeadId", "getCustomerId", "getContactId", "getOpportunityId"};
        for (String methodName : idGetters) {
            try {
                Method method = obj.getClass().getMethod(methodName);
                Object id = method.invoke(obj);
                if (id instanceof Long) {
                    return (Long) id;
                }
                if (id instanceof Integer) {
                    return ((Integer) id).longValue();
                }
            } catch (NoSuchMethodException e) {
                // 方法不存在，继续尝试下一个
            } catch (Exception e) {
                logger.debug("通过方法 {} 获取ID失败: {}", methodName, e.getMessage());
            }
        }
        return null;
    }
    
    /**
     * 记录执行前数据
     */
    private void recordBeforeData(ProceedingJoinPoint joinPoint, BusinessLogContext context) {
        try {
            if (context.getOperationType() == OperationType.UPDATE) {
                // 可以在此查询旧数据，用于后续比较
            }
        } catch (Exception e) {
            logger.warn("记录执行前数据失败", e);
        }
    }
    
    /**
     * 记录执行后数据
     */
    private void recordAfterData(ProceedingJoinPoint joinPoint, BusinessLogContext context, Object result) {
        try {
            // 智能提取业务ID
            extractBusinessId(joinPoint, context, result);

            // 设置响应结果
            if (result != null) {
                context.setResponseResult(JSON.toJSONString(result));
            }
            
            // 记录字段变更
            if (context.getOperationType() == OperationType.UPDATE) {
                List<FieldChange> fieldChanges = analyzeFieldChanges(joinPoint, context);
                context.setFieldChanges(fieldChanges);
            }
            
        } catch (Exception e) {
            logger.warn("记录执行后数据失败", e);
        }
    }
    
    /**
     * 分析字段变更
     */
    private List<FieldChange> analyzeFieldChanges(ProceedingJoinPoint joinPoint, BusinessLogContext context) {
        List<FieldChange> fieldChanges = new ArrayList<>();
        try {
            Object[] args = joinPoint.getArgs();
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Parameter[] parameters = signature.getMethod().getParameters();
            
            for (int i = 0; i < parameters.length; i++) {
                if (parameters[i].isAnnotationPresent(RequestBody.class)) {
                    if (args[i] != null) {
                        analyzeObjectChanges(args[i], fieldChanges);
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("分析字段变更失败", e);
        }
        return fieldChanges;
    }
    
    /**
     * 分析对象变更
     */
    private void analyzeObjectChanges(Object obj, List<FieldChange> fieldChanges) {
        try {
            // 这是一个简化的实现，实际项目中需要与旧对象进行比对
            FieldChange change = new FieldChange();
            change.setFieldName("object");
            change.setFieldLabel("对象");
            change.setOldValue(null); // 实际应为旧值
            change.setNewValue(obj.toString());
            change.setChangeType("UPDATE");
            fieldChanges.add(change);
        } catch (Exception e) {
            logger.warn("分析对象变更失败", e);
        }
    }

    /**
     * 检查当前是否为测试环境
     */
    private boolean isTestProfileActive() {
        for (String profile : environment.getActiveProfiles()) {
            if ("test".equalsIgnoreCase(profile)) {
                return true;
            }
        }
        return false;
    }
}
