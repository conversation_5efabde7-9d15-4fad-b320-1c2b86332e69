package com.ruoyi.crm.service.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.domain.entity.CrmOrder;
import com.ruoyi.common.domain.entity.CrmOrderItem;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.mapper.CrmOrderItemMapper;
import com.ruoyi.common.mapper.CrmOrderMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.crm.service.ICrmOrderService;
// import com.ruoyi.framework.config.OssConfig;

/**
 * 3D打印订单服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Service
public class CrmOrderServiceImpl implements ICrmOrderService {
    @Autowired
    private CrmOrderMapper crmOrderMapper;

    @Autowired
    private CrmOrderItemMapper crmOrderItemMapper;

    // @Autowired
    // private OssConfig ossConfig;

    /**
     * 查询订单
     * 
     * @param id 订单主键
     * @return 订单
     */
    @Override
    public CrmOrder selectCrmOrderById(Long id) {
        CrmOrder order = crmOrderMapper.selectCrmOrderById(id);
        if (order != null) {
            // 查询订单项
            List<CrmOrderItem> orderItems = crmOrderItemMapper.selectCrmOrderItemByOrderId(id);
            order.setOrderItems(orderItems);
        }
        return order;
    }

    /**
     * 查询订单列表
     * 
     * @param crmOrder 订单
     * @return 订单
     */
    @Override
    public List<CrmOrder> selectCrmOrderList(CrmOrder crmOrder) {
        return crmOrderMapper.selectCrmOrderList(crmOrder);
    }

    /**
     * 新增订单
     * 
     * @param crmOrder 订单
     * @return 结果
     */
    @Override
    @Transactional
    public int insertCrmOrder(CrmOrder crmOrder) {
        crmOrder.setCreateTime(DateUtils.getNowDate());
        crmOrder.setCreatedBy(SecurityUtils.getUserId());
        
        int rows = crmOrderMapper.insertCrmOrder(crmOrder);
        
        // 插入订单项
        if (crmOrder.getOrderItems() != null && !crmOrder.getOrderItems().isEmpty()) {
            for (CrmOrderItem item : crmOrder.getOrderItems()) {
                item.setOrderId(crmOrder.getId());
                item.setCreateTime(DateUtils.getNowDate());
                item.setCreatedBy(SecurityUtils.getUserId());
                crmOrderItemMapper.insertCrmOrderItem(item);
            }
        }
        
        return rows;
    }

    /**
     * 修改订单
     * 
     * @param crmOrder 订单
     * @return 结果
     */
    @Override
    @Transactional
    public int updateCrmOrder(CrmOrder crmOrder) {
        crmOrder.setUpdateTime(DateUtils.getNowDate());
        crmOrder.setUpdatedBy(SecurityUtils.getUserId());
        
        // 删除原有订单项
        crmOrderItemMapper.deleteCrmOrderItemByOrderId(crmOrder.getId());
        
        // 重新插入订单项
        if (crmOrder.getOrderItems() != null && !crmOrder.getOrderItems().isEmpty()) {
            for (CrmOrderItem item : crmOrder.getOrderItems()) {
                item.setOrderId(crmOrder.getId());
                item.setUpdateTime(DateUtils.getNowDate());
                item.setUpdatedBy(SecurityUtils.getUserId());
                crmOrderItemMapper.insertCrmOrderItem(item);
            }
        }
        
        return crmOrderMapper.updateCrmOrder(crmOrder);
    }

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCrmOrderByIds(Long[] ids) {
        // 删除订单项
        for (Long id : ids) {
            crmOrderItemMapper.deleteCrmOrderItemByOrderId(id);
        }
        
        return crmOrderMapper.deleteCrmOrderByIds(ids);
    }

    /**
     * 删除订单信息
     * 
     * @param id 订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCrmOrderById(Long id) {
        // 删除订单项
        crmOrderItemMapper.deleteCrmOrderItemByOrderId(id);
        
        return crmOrderMapper.deleteCrmOrderById(id);
    }

    /**
     * 创建3D打印订单
     * 
     * @param quoteData 报价数据(JSON字符串)
     * @param customerInfo 客户信息(JSON字符串)
     * @param files 上传的文件
     * @return 订单信息
     */
    @Override
    @Transactional
    public CrmOrder createPrintOrder(String quoteData, String customerInfo, MultipartFile[] files) {
        try {
            // 解析报价数据
            JSONObject quoteJson = JSON.parseObject(quoteData);
            JSONObject customerJson = JSON.parseObject(customerInfo);
            
            // 创建订单
            CrmOrder order = new CrmOrder();
            order.setOrderNo(generateOrderNo());
            order.setQuoteNo(quoteJson.getString("quoteNo"));
            order.setCustomerName(customerJson.getString("customerName"));
            order.setContactPerson(customerJson.getString("contactPerson"));
            order.setContactPhone(customerJson.getString("contactPhone"));
            order.setDeliveryAddress(customerJson.getString("deliveryAddress"));
            order.setTotalAmount(quoteJson.getBigDecimal("totalAmount"));
            order.setStatus("pending");
            order.setCreateTime(DateUtils.getNowDate());
            order.setCreatedBy(SecurityUtils.getUserId());
            
            // 设置预计发货日期（假设3个工作日）
            order.setEstimatedDeliveryDate(new Timestamp(System.currentTimeMillis() + 3 * 24 * 60 * 60 * 1000L));
            
            // 处理上传的文件
            List<String> fileUrls = new ArrayList<>();
            if (files != null && files.length > 0) {
                for (MultipartFile file : files) {
                    if (!file.isEmpty()) {
                        // 这里应该上传到OSS并获取URL
                        String fileUrl = uploadToOss(file);
                        fileUrls.add(fileUrl);
                    }
                }
            }
            
            // 插入订单
            int rows = crmOrderMapper.insertCrmOrder(order);
            
            if (rows > 0) {
                // 创建订单项
                JSONArray items = quoteJson.getJSONArray("items");
                List<CrmOrderItem> orderItems = new ArrayList<>();
                
                for (int i = 0; i < items.size(); i++) {
                    JSONObject item = items.getJSONObject(i);
                    JSONObject modelInfo = item.getJSONObject("modelInfo");
                    
                    CrmOrderItem orderItem = new CrmOrderItem();
                    orderItem.setOrderId(order.getId());
                    orderItem.setModelName(item.getString("modelName"));
                    orderItem.setDimensions(modelInfo.getString("dimensions"));
                    orderItem.setVolume(modelInfo.getString("volume"));
                    orderItem.setSurfaceArea(modelInfo.getString("surfaceArea"));
                    orderItem.setMaterialName(item.getString("material"));
                    orderItem.setQuantity(item.getInteger("quantity"));
                    orderItem.setUnitPrice(item.getBigDecimal("unitPrice"));
                    orderItem.setTotalPrice(item.getBigDecimal("totalPrice"));
                    
                    // 处理后处理选项
                    JSONArray processOptions = item.getJSONArray("processOptions");
                    if (processOptions != null && !processOptions.isEmpty()) {
                        orderItem.setProcessOptions(String.join(",", processOptions.toJavaList(String.class)));
                    }
                    
                    orderItem.setCreateTime(DateUtils.getNowDate());
                    orderItem.setCreatedBy(SecurityUtils.getUserId());
                    
                    // 如果有对应的文件URL，设置到订单项
                    if (i < fileUrls.size()) {
                        orderItem.setModelFileUrl(fileUrls.get(i));
                    }
                    
                    orderItems.add(orderItem);
                    crmOrderItemMapper.insertCrmOrderItem(orderItem);
                }
                
                order.setOrderItems(orderItems);
                order.setFileUrls(fileUrls);
            }
            
            return order;
            
        } catch (Exception e) {
            throw new ServiceException("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单编号查询订单
     * 
     * @param orderNo 订单编号
     * @return 订单
     */
    @Override
    public CrmOrder selectCrmOrderByOrderNo(String orderNo) {
        return crmOrderMapper.selectCrmOrderByOrderNo(orderNo);
    }

    /**
     * 更新订单状态
     * 
     * @param id 订单ID
     * @param status 新状态
     * @return 结果
     */
    @Override
    public int updateOrderStatus(Long id, String status) {
        CrmOrder order = new CrmOrder();
        order.setId(id);
        order.setStatus(status);
        order.setUpdateTime(DateUtils.getNowDate());
        order.setUpdatedBy(SecurityUtils.getUserId());
        
        return crmOrderMapper.updateCrmOrder(order);
    }

    /**
     * 生成订单编号
     * 
     * @return 订单编号
     */
    private String generateOrderNo() {
        // 格式：OR + 年月日 + 6位随机数
        String dateStr = DateUtils.dateTimeNow("yyyyMMdd");
        String randomStr = String.valueOf((int) (Math.random() * 900000) + 100000);
        return "OR" + dateStr + randomStr;
    }

    /**
     * 上传文件到OSS
     * 
     * @param file 文件
     * @return 文件URL
     */
    private String uploadToOss(MultipartFile file) {
        // 这里应该实现OSS上传逻辑
        // 暂时返回一个模拟的URL
        return "https://oss.example.com/3d-models/" + System.currentTimeMillis() + "_" + file.getOriginalFilename();
    }
}
