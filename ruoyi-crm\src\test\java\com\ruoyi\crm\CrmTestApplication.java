package com.ruoyi.crm;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import com.ruoyi.framework.web.service.PermissionService;

/**
 * CRM测试应用启动类
 * 简化配置，避免与其他配置冲突
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@SpringBootApplication
@Import(PermissionService.class) // 精确导入PermissionService，避免引入其他冲突
@ComponentScan(basePackages = {
    "com.ruoyi.crm",
    "com.ruoyi.common",
    "com.ruoyi.activiti"
}
)
@MapperScan(basePackages = {
    "com.ruoyi.common.mapper", 
    "com.ruoyi.crm.mapper",
    "com.ruoyi.activiti.mapper"
})
public class CrmTestApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(CrmTestApplication.class, args);
    }
}
